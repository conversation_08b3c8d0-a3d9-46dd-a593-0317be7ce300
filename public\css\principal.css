/* Global Variables */
:root {
    --primary-color: #0d47a1;
    --primary-light: #1565c0;
    --primary-dark: #002171;
    --accent-color: #ffc107;
    --text-dark: #333333;
    --text-light: #666666;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
}

/* Global Styles */
body {
    font-family: 'Inter', 'Roboto', sans-serif;
    padding-top: 76px;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    letter-spacing: -0.02em;
}

p {
    font-size: 1rem;
    line-height: 1.8;
    color: var(--text-light);
}

/* Navbar Styles */
.navbar {
    padding: 1rem 0;
    background-color: var(--white) !important;
    box-shadow: 0 2px 15px rgba(0,0,0,0.04);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--primary-color) !important;
    font-weight: 700;
}

.navbar-brand span {
    font-size: 1.25rem;
    letter-spacing: -0.01em;
}

.navbar-brand img {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-brand:hover img {
    transform: scale(1.05) rotate(-2deg);
}

.nav-link {
    color: var(--primary-color) !important;
    font-weight: 500;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.5rem 1rem !important;
    margin: 0 0.25rem;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--primary-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--primary-light) !important;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link.active {
    color: var(--primary-light) !important;
}

.nav-link.active::after {
    width: 100%;
}

.btn-custom {
    background-color: var(--primary-color) !important;
    color: var(--white) !important;
    font-weight: 600;
    border-radius: 8px;
    padding: 0.625rem 1.5rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
}

.btn-custom:hover {
    background-color: var(--primary-light) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(13, 71, 161, 0.15);
}

/* Hero Sections */
.hero-section {
    padding: 140px 0;
    background-color: var(--gray-100);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(13, 71, 161, 0.03) 0%, rgba(21, 101, 192, 0.03) 100%);
    z-index: 1;
}

.hero-section > * {
    position: relative;
    z-index: 2;
}

.hero-section-small {
    padding: 120px 0 80px;
    background-color: var(--gray-100);
    position: relative;
}

.hero-title {
    color: var(--primary-color);
    font-weight: 800;
    font-size: 3rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    color: var(--text-light);
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    font-weight: 400;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--white);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.card-body {
    padding: 2rem;
}

.card-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1rem;
}

.card-text {
    color: var(--text-light);
    line-height: 1.7;
}

/* Section Styles */
.section-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 2.5rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background-color: var(--primary-light);
    border-radius: 2px;
}

/* Form Styles */
.form-control {
    border-radius: 12px;
    padding: 0.875rem 1.25rem;
    border: 2px solid var(--gray-200);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 4px rgba(13, 71, 161, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* Animation Styles */
[data-aos] {
    transition-duration: 800ms !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.15rem;
    }
    
    .navbar-brand span {
        font-size: 1.15rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .navbar-brand span {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .hero-section {
        padding: 100px 0;
    }
    
    .hero-section-small {
        padding: 80px 0 60px;
    }
} 