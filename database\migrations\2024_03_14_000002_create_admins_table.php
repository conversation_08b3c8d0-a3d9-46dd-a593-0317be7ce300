<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;

return new class extends Migration
{
    public function up()
    {
        Schema::create('admins', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('username')->unique();
            $table->string('email')->unique();
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
        });

        // Create default admin user
        Admin::create([
            'name' => 'Admin User',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
        ]);
    }

    public function down()
    {
        Schema::dropIfExists('admins');
    }
};