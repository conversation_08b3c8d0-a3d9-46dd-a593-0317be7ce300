<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Teacher;
use App\Models\Student;
use App\Mail\TeacherCredentialsMail;
use App\Services\TeacherEmailService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $teachers = Teacher::orderBy('name')->get();

        // Get all available grade levels for the filter dropdown
        $availableGradeLevels = Student::select('grade_level')
            ->distinct()
            ->whereNotNull('grade_level')
            ->orderBy('grade_level')
            ->pluck('grade_level');

        // Build the students query
        $studentsQuery = Student::query();

        // Apply grade level filter if provided
        $selectedGradeLevel = $request->get('grade_level');
        if ($selectedGradeLevel && $selectedGradeLevel !== 'all') {
            $studentsQuery->where('grade_level', $selectedGradeLevel);
        }

        $students = $studentsQuery->orderBy('first_name')->get();

        // Count students by grade level for statistics
        $studentsByGrade = Student::select('grade_level', \DB::raw('count(*) as count'))
            ->groupBy('grade_level')
            ->orderBy('grade_level')
            ->get();

        return view('admin.users.index', compact(
            'teachers',
            'students',
            'availableGradeLevels',
            'selectedGradeLevel',
            'studentsByGrade'
        ));
    }

    // Dedicated teacher management index
    public function indexTeachers()
    {
        $teachers = Teacher::orderBy('name')->get();
        return view('admin.users.teachers-index', compact('teachers'));
    }

    /**
     * Display only students (for the new workflow where students are created via credentials)
     */
    public function indexStudents(Request $request)
    {
        // Get all available grade levels for the filter dropdown
        $availableGradeLevels = Student::select('grade_level')
            ->distinct()
            ->whereNotNull('grade_level')
            ->orderBy('grade_level')
            ->pluck('grade_level');

        // Build the students query
        $studentsQuery = Student::query();

        // Apply grade level filter if provided
        $selectedGradeLevel = $request->get('grade_level');
        if ($selectedGradeLevel && $selectedGradeLevel !== 'all') {
            $studentsQuery->where('grade_level', $selectedGradeLevel);
        }

        // Apply search filter if provided
        if ($request->filled('search')) {
            $search = $request->search;
            $studentsQuery->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('student_id', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply account type filter
        if ($request->filled('account_type')) {
            if ($request->account_type === 'temporary') {
                $studentsQuery->where('is_temporary_account', true);
            } elseif ($request->account_type === 'completed') {
                $studentsQuery->where('profile_completed', true);
            }
        }

        $students = $studentsQuery->orderBy('created_at', 'desc')->paginate(20);

        // Count students by grade level for statistics
        $studentsByGrade = Student::select('grade_level', \DB::raw('count(*) as count'))
            ->groupBy('grade_level')
            ->orderBy('grade_level')
            ->get();

        // Additional statistics
        $totalStudents = Student::count();
        $temporaryAccounts = Student::where('is_temporary_account', true)->count();
        $completedProfiles = Student::where('profile_completed', true)->count();

        return view('admin.users.students-index', compact(
            'students',
            'availableGradeLevels',
            'selectedGradeLevel',
            'studentsByGrade',
            'totalStudents',
            'temporaryAccounts',
            'completedProfiles'
        ));
    }

    // Teacher Management
    public function createTeacher()
    {
        return view('admin.users.create-teacher');
    }

    public function storeTeacher(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:teachers',
            'password' => 'required|string|min:8|confirmed',
            'track' => 'nullable|string|max:255',
            'cluster' => 'nullable|string|max:255',
            'contact_number' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'status' => 'required|in:active,inactive',
        ]);

        // Store the plain password before hashing for email
        $plainPassword = $validated['password'];

        // Hash the password for database storage
        $validated['password'] = Hash::make($validated['password']);

        // Create the teacher
        $teacher = Teacher::create($validated);

        // Send credentials email using the email service
        $emailService = new TeacherEmailService();
        $emailResult = $emailService->sendCredentialsEmail($teacher, $plainPassword, route('login'));

        if ($emailResult['success']) {
            return redirect()->route('admin.users')
                ->with('success', 'Teacher account created successfully!')
                ->with('email_success', 'Login credentials have been sent to ' . $teacher->email . '. The teacher should receive the email within a few minutes.');
        } else {
            return redirect()->route('admin.users')
                ->with('success', 'Teacher account created successfully!')
                ->with('email_warning', 'However, there was an issue sending the credentials email to ' . $teacher->email . '. Please provide the login details manually. Error: ' . $emailResult['message']);
        }
    }

    public function editTeacher(Teacher $teacher)
    {
        return view('admin.users.edit-teacher', compact('teacher'));
    }

    /**
     * Show email configuration test page
     */
    public function showEmailTest()
    {
        $emailService = new TeacherEmailService();
        $configStatus = $emailService->getEmailConfigurationStatus();

        return view('admin.users.email-test', compact('configStatus'));
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        $emailService = new TeacherEmailService();
        $result = $emailService->testEmailConfiguration($request->test_email);

        if ($result['success']) {
            return redirect()->back()->with('email_success', $result['message']);
        } else {
            return redirect()->back()->with('email_warning', $result['message']);
        }
    }

    public function updateTeacher(Request $request, Teacher $teacher)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('teachers')->ignore($teacher->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'track' => 'nullable|string|max:255',
            'cluster' => 'nullable|string|max:255',
            'contact_number' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:500',
            'status' => 'required|in:active,inactive',
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $teacher->update($validated);

        return redirect()->route('admin.users')->with('success', 'Teacher updated successfully.');
    }

    public function destroyTeacher(Teacher $teacher)
    {
        $teacher->delete();
        return redirect()->route('admin.users')->with('success', 'Teacher deleted successfully.');
    }

    // Student Management (Students are now created via credential login only)



    public function showStudent(Student $student)
    {
        try {
            // Load relationships for comprehensive profile data
            $student->load(['subjects', 'grades.subject']);

            // Get academic performance statistics
            $totalSubjects = $student->subjects()->count();

            // Handle grades - check if grades exist and have the right structure
            $gradesWithScores = collect();
            $averageGrade = null;
            $passedSubjects = 0;
            $failedSubjects = 0;
            $recentGrades = collect();

            // Try to get grades from the pivot table first
            $subjectsWithGrades = $student->subjects()->wherePivot('grade', '!=', null)->get();

            if ($subjectsWithGrades->count() > 0) {
                foreach ($subjectsWithGrades as $subject) {
                    $grade = $subject->pivot->grade;
                    if ($grade) {
                        $gradesWithScores->push((object)[
                            'grade' => $grade,
                            'subject' => $subject,
                            'created_at' => $subject->pivot->created_at
                        ]);

                        if ($grade >= 75) {
                            $passedSubjects++;
                        } else {
                            $failedSubjects++;
                        }
                    }
                }

                $averageGrade = $gradesWithScores->avg('grade');
                $recentGrades = $gradesWithScores->sortByDesc('created_at')->take(5);
            }

            // Get subjects grouped by grade level
            $subjectsByGrade = $student->subjects()->get()->groupBy('grade_level');

            // Calculate GPA if grades exist
            $gpa = null;
            if ($gradesWithScores->count() > 0) {
                $gpa = $this->calculateGPA($gradesWithScores);
            }

            return view('admin.users.show-student', compact(
                'student',
                'totalSubjects',
                'averageGrade',
                'passedSubjects',
                'failedSubjects',
                'recentGrades',
                'subjectsByGrade',
                'gpa'
            ));

        } catch (\Exception $e) {
            // If there's an error, show basic profile without grades
            return view('admin.users.show-student', [
                'student' => $student,
                'totalSubjects' => 0,
                'averageGrade' => null,
                'passedSubjects' => 0,
                'failedSubjects' => 0,
                'recentGrades' => collect(),
                'subjectsByGrade' => collect(),
                'gpa' => null
            ]);
        }
    }

    private function calculateGPA($grades)
    {
        $totalPoints = 0;
        $totalUnits = 0;

        foreach ($grades as $grade) {
            $gradePoint = $this->convertToGradePoint($grade->grade);
            $units = $grade->subject->units ?? 1;

            $totalPoints += $gradePoint * $units;
            $totalUnits += $units;
        }

        return $totalUnits > 0 ? round($totalPoints / $totalUnits, 2) : 0;
    }

    private function convertToGradePoint($numericGrade)
    {
        if ($numericGrade >= 97) return 4.0;
        if ($numericGrade >= 94) return 3.7;
        if ($numericGrade >= 91) return 3.3;
        if ($numericGrade >= 88) return 3.0;
        if ($numericGrade >= 85) return 2.7;
        if ($numericGrade >= 82) return 2.3;
        if ($numericGrade >= 79) return 2.0;
        if ($numericGrade >= 76) return 1.7;
        if ($numericGrade >= 75) return 1.0;
        return 0.0;
    }

    public function destroyStudent(Student $student)
    {
        $student->delete();
        return redirect()->route('admin.users')->with('success', 'Student deleted successfully.');
    }

    /**
     * Admin resets a student's password
     */
    public function resetStudentPassword(Request $request, Student $student)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $student->password = \Hash::make($request->password);
        $student->is_temporary_account = false;
        $student->profile_completed = true;
        $student->save();

        return redirect()->back()->with('success', 'Password has been reset successfully. New password: ' . $request->password);
    }
}