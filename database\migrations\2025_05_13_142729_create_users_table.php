<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable(); // For teacher full name
            $table->string('first_name')->nullable(); // For student
            $table->string('last_name')->nullable(); // For student
            $table->string('email')->unique();
            $table->string('username')->nullable()->unique();
            $table->string('student_id')->unique();
            $table->string('password');
            $table->enum('role', ['admin', 'teacher', 'student']);
            // Student-specific fields
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_relationship')->nullable();
            $table->string('emergency_contact_number')->nullable();
            $table->string('grade_level')->nullable();
            $table->string('strand')->nullable();
            $table->string('track')->nullable();
            $table->string('section')->nullable();
            $table->string('gender')->nullable();
            $table->text('address')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
