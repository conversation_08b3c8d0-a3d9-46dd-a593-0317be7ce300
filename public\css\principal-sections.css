/* Common Variables */
:root {
    --primary-blue: #1e3a8a;
    --primary-blue-light: #2563eb;
    --primary-blue-dark: #1e3a8a;
    --primary-orange: #f97316;
    --primary-orange-light: #fb923c;
    --primary-yellow: #fbbf24;
    --accent-color: #fbbf24;
    --success-color: #22c55e;
    --warning-color: #f97316;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --white: #ffffff;
}

/* Common Card Styles */
.card {
    background: var(--white);
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

/* Teachers Section Styles */
.faculty-directory-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.faculty-directory-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--white);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faculty-directory-body {
    padding: 1.5rem;
}

.faculty-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.faculty-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

.faculty-table th {
    background: var(--gray-50);
    color: var(--text-dark);
    font-weight: 600;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid var(--gray-200);
    white-space: nowrap;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.faculty-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.faculty-table tbody tr:hover {
    background-color: var(--gray-50);
}

.faculty-table td:first-child,
.faculty-table th:first-child {
    padding-left: 1.5rem;
}

.faculty-table td:last-child,
.faculty-table th:last-child {
    padding-right: 1.5rem;
    text-align: right;
}

/* Column specific styles */
.faculty-table th:nth-child(1),
.faculty-table td:nth-child(1) {
    width: 20%;
}

.faculty-table th:nth-child(2),
.faculty-table td:nth-child(2) {
    width: 20%;
}

.faculty-table th:nth-child(3),
.faculty-table td:nth-child(3) {
    width: 15%;
}

.faculty-table th:nth-child(4),
.faculty-table td:nth-child(4) {
    width: 15%;
}

.faculty-table th:nth-child(5),
.faculty-table td:nth-child(5) {
    width: 10%;
}

.faculty-table th:nth-child(6),
.faculty-table td:nth-child(6) {
    width: 10%;
}

.faculty-table th:nth-child(7),
.faculty-table td:nth-child(7) {
    width: 10%;
    text-align: right;
}

/* Status badge styles */
.faculty-table .badge {
    padding: 0.5rem 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
}

/* Action buttons styles */
.faculty-table .btn-sm {
    padding: 0.4rem 0.75rem;
    margin: 0 0.25rem;
}

.faculty-table .btn-sm i {
    font-size: 0.875rem;
}

/* Registration Steps */
.registration-steps {
    padding: 1.5rem;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.step-number {
    background: var(--primary-light);
    color: var(--white);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

/* Announcements Section Styles */
.announcements-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.announcements-card-header {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    color: var(--white);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.announcement-stats {
    display: flex;
    gap: 0.75rem;
}

.stat-badge {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-total {
    background: rgba(59, 130, 246, 0.2);
    color: var(--white);
}

.stat-active {
    background: rgba(16, 185, 129, 0.2);
    color: var(--white);
}

.stat-draft {
    background: rgba(245, 158, 11, 0.2);
    color: var(--white);
}

/* Announcements Table Styles */
.announcements-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
    background: var(--white);
}

.announcements-table th,
.announcements-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    vertical-align: middle;
}

.announcements-table th {
    background: linear-gradient(to bottom, var(--primary-blue-light), var(--primary-blue));
    color: var(--white);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    white-space: nowrap;
    border-bottom: 2px solid var(--gray-200);
}

.announcements-table tbody tr {
    transition: all 0.2s ease;
}

.announcements-table tbody tr:hover {
    background-color: var(--gray-50);
}

/* Column specific styles */
.announcement-title {
    font-weight: 500;
    color: var(--text-dark);
    padding-right: 2rem;
}

.announcement-status {
    text-align: center;
    width: 140px;
}

.announcement-date {
    color: var(--text-light);
    font-size: 0.875rem;
    width: 160px;
    white-space: nowrap;
}

.announcement-actions {
    width: 220px;
    text-align: right;
    padding-right: 1rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    align-items: center;
}

/* Status badge styles */
.announcements-table .badge {
    padding: 0.5rem 1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    display: inline-block;
    min-width: 90px;
    text-align: center;
}

/* Action button styles */
.announcements-table .btn-sm {
    padding: 0.4rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 32px;
    justify-content: center;
}

.announcements-table .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.announcements-table .btn-sm i {
    font-size: 0.875rem;
}

/* Empty state styles */
.empty-state {
    padding: 3rem 1.5rem;
    text-align: center;
}

.empty-state i {
    font-size: 2.5rem;
    color: var(--gray-300);
    margin-bottom: 1rem;
}

/* Responsive styles */
@media (max-width: 992px) {
    .announcements-table .button-text {
        display: none;
    }

    .announcements-table .btn-sm {
        padding: 0.4rem;
        min-width: 28px;
    }

    .announcement-status {
        width: 100px;
    }

    .announcement-date {
        width: 120px;
    }

    .announcement-actions {
        width: 150px;
    }

    .action-buttons {
        gap: 0.25rem;
    }

    .announcements-table th,
    .announcements-table td {
        padding: 0.75rem 1rem;
    }

    .announcements-table .badge {
        min-width: 70px;
        padding: 0.4rem 0.5rem;
    }
}

@media (max-width: 768px) {
    .announcement-date {
        display: none;
    }

    .announcement-status {
        width: 80px;
    }

    .announcement-actions {
        width: 120px;
    }

    .announcements-table th,
    .announcements-table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Profile Section Styles */
.profile-picture-container {
    position: relative;
    display: inline-block;
}

.profile-picture-container img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border: 4px solid var(--primary-light);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.profile-picture-overlay {
    opacity: 0;
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
}

.profile-picture-container:hover .profile-picture-overlay {
    opacity: 1;
}

.profile-picture-overlay .btn-group-vertical {
    gap: 0.5rem;
}

.btn-edit-profile {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-edit-profile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    color: var(--white);
}

.profile-card {
    border: none;
    border-radius: 16px;
    background: var(--white);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.profile-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--white);
    padding: 2rem;
    border-radius: 16px 16px 0 0;
}

.profile-body {
    padding: 2rem;
}

.profile-info-card {
    background: var(--gray-50);
    border-radius: 12px;
    padding: 1.5rem;
    height: 100%;
}

.profile-info-card h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 1rem;
}

.social-link {
    background: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .faculty-directory-header,
    .announcements-card-header {
        flex-direction: column;
        gap: 1rem;
    }

    .faculty-stats,
    .announcement-stats {
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .profile-picture-container img {
        width: 120px;
        height: 120px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue-light) 0%, var(--primary-blue) 100%);
    border: none;
    color: var(--white);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
    border: none;
    color: var(--white);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--primary-orange-light) 0%, var(--primary-orange) 100%);
    color: var(--white);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #16a34a 100%);
    border: none;
    color: var(--white);
}

.btn-success:hover {
    background: linear-gradient(135deg, #16a34a 0%, var(--success-color) 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #2563eb 100%);
    border: none;
    color: var(--white);
}

.btn-info:hover {
    background: linear-gradient(135deg, #2563eb 0%, var(--info-color) 100%);
    color: var(--white);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    border: none;
    color: var(--white);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, var(--danger-color) 100%);
}

/* Header Styles */
.announcements-header {
    background: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-yellow) 100%);
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.announcements-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-blue-light) 0%, var(--primary-blue) 100%);
    clip-path: polygon(100% 0, 100% 100%, 0 100%, 20% 0);
    opacity: 0.9;
    z-index: 1;
}

.school-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 2;
    position: relative;
}

.logo-img {
    width: 50px;
    height: 50px;
    object-fit: contain;
}

.page-title {
    color: var(--primary-blue);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.page-subtitle {
    color: var(--text-dark);
    font-size: 0.95rem;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

/* Button Styles */
.btn-outline-warning {
    color: var(--primary-orange);
    border-color: var(--primary-orange);
}

.btn-outline-warning:hover {
    background-color: var(--primary-orange);
    border-color: var(--primary-orange);
    color: var(--white);
}

.btn-outline-primary {
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-outline-primary:hover {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

/* Stats Badge Styles */
.stat-badge {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
    backdrop-filter: blur(8px);
}

.stat-total {
    background: rgba(255, 255, 255, 0.2);
}

.stat-active {
    background: rgba(34, 197, 94, 0.2);
}

.stat-draft {
    background: rgba(249, 115, 22, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .announcements-header {
        padding: 1.5rem;
    }

    .school-logo {
        width: 50px;
        height: 50px;
    }

    .logo-img {
        width: 40px;
        height: 40px;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .page-subtitle {
        font-size: 0.875rem;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
    }
} 