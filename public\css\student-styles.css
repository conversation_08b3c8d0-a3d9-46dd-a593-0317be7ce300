/* Student Portal Styles */
:root {
    --primary-blue: #012970;
    --accent-orange: #FF6B35;
    --accent-blue: #0D6EFD;
    --success-green: #00B74A;
    --white: #FFFFFF;
    --light-gray: #F8F9FA;
    --text-dark: #2B2B2B;
    --text-muted: #6C757D;
    --border-radius: 15px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Font Family */
* {
    font-family: 'Poppins', sans-serif;
}

/* Sidebar Styles */
.sidebar {
    background-color: var(--primary-blue);
    width: 280px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    padding: 2rem 1.5rem;
    color: var(--white);
    z-index: 1000;
    transition: var(--transition);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2.5rem;
    padding: 0.5rem;
}

.sidebar-brand img {
    width: 40px;
    height: 40px;
}

.sidebar-brand h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--white);
    margin: 0;
}

.profile-section {
    text-align: center;
    padding: 1.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1.5rem;
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 1rem;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.online-status {
    background-color: var(--success-green);
    color: var(--white);
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    display: inline-block;
    margin-top: 0.5rem;
}

/* Navigation Menu */
.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.nav-link i {
    margin-right: 1rem;
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

/* Main Content Area */
.main-content {
    margin-left: 280px;
    padding: 2rem;
    min-height: 100vh;
    background: linear-gradient(135deg, #f0f7ff 0%, #e3f2ff 100%);
}

/* Header Section */
.content-header {
    background: linear-gradient(135deg, var(--accent-orange) 0%, #FF8B5E 100%);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.content-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
    transform: skewX(-30deg);
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.header-subtitle {
    font-size: 1rem;
    opacity: 0.9;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-action {
    padding: 0.5rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn-create {
    background-color: var(--success-green);
    color: var(--white);
}

.btn-refresh {
    background-color: var(--accent-blue);
    color: var(--white);
}

.btn-cleanup {
    background-color: var(--white);
    color: var(--text-dark);
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Table Styles */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.table-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
}

.filter-dropdown {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: var(--white);
    color: var(--text-dark);
}

/* Status Badges */
.badge {
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.badge-active {
    background-color: rgba(0, 183, 74, 0.1);
    color: var(--success-green);
}

/* Action Buttons in Table */
.btn-table-action {
    padding: 0.35rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn-draft {
    background-color: #FF6B35;
    color: var(--white);
}

.btn-edit {
    background-color: var(--accent-blue);
    color: var(--white);
}

.btn-delete {
    background-color: #dc3545;
    color: var(--white);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 240px;
    }
    
    .main-content {
        margin-left: 240px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .content-header {
        padding: 1.5rem;
    }
    
    .header-title {
        font-size: 1.5rem;
    }
} 