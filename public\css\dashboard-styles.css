/* Common Dashboard Styles */
:root {
    --primary-color: #2563eb;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --gray-300: #dddfeb;
    --gray-800: #5a5c69;
}

/* Global Font Family */
* {
    font-family: 'Poppins', sans-serif;
}

/* Main Content Layout */
.main-content {
    padding: 2rem;
    background: #ffffff;
    min-height: calc(100vh - 80px);
    position: relative;
    overflow-x: hidden;
    margin-left: 250px;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.01) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.border-left-primary { border-left: 0.25rem solid var(--primary-color) !important; }
.border-left-success { border-left: 0.25rem solid var(--success-color) !important; }
.border-left-info { border-left: 0.25rem solid var(--info-color) !important; }
.border-left-warning { border-left: 0.25rem solid var(--warning-color) !important; }

/* Stat Cards */
.stat-card {
    background: #ffffff;
    padding: 2rem;
    border-radius: 20px;
    box-shadow:
        0 4px 20px rgba(37, 99, 235, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    border-radius: 20px 20px 0 0;
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow:
        0 8px 30px rgba(37, 99, 235, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-card-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 1rem;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 15px;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-card-content {
    flex: 1;
}

.stat-card-title {
    font-size: 0.95rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-card-value {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.stat-card-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* Quick Actions */
.quick-action-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ffffff;
}

.quick-action-content h6 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.quick-action-content p {
    margin: 0;
    font-size: 0.875rem;
    color: #6b7280;
}

/* Page Header */
.page-header {
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--gray-800);
}

.page-subtitle {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

/* Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.grid-item {
    background: #ffffff;
    padding: 2rem;
    border-radius: 20px;
    box-shadow:
        0 4px 20px rgba(37, 99, 235, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.grid-item:hover {
    transform: translateY(-5px);
    box-shadow:
        0 8px 30px rgba(37, 99, 235, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Lists */
.subjects-list,
.announcements-list,
.activities-list,
.grades-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

/* Utility Classes */
.text-gray-300 { color: var(--gray-300) !important; }
.text-gray-800 { color: var(--gray-800) !important; }

/* Responsive Adjustments */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .page-actions {
        flex-direction: column;
    }
} 